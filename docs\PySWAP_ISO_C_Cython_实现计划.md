## 架构概述

本方案目标：在不侵入SWAP420求解核心的前提下，利用 Fortran ISO C Binding + C 头文件 + Cython，为Python提供一个稳定、可测试的PySWAP绑定层。核心思路：
- 在 Fortran 层基于现有接口 `subroutine swap(iCaller, iTask, toswap, fromswap)`（见 swap420/swap.f90）增加一个仅面向C调用者的薄封装层（ISO C Binding），将 `type(swap_input)` 与 `type(swap_output)` 映射为C可见的扁平结构体（Plain Old Data），并暴露 `swap_init / swap_step / swap_close` 三个稳定函数。
- 通过一个小型C桥接头/源文件（声明extern "C"函数与结构体）向Cython暴露接口。
- 在 Cython 层编写 `pyswap/_core.pyx`，对C结构体做Python化的属性访问与类型转换（numpy数组视图），并提供面向用户的 `SwapSession` 类：
  - init() → 调用 swap_init 完成初始化并取回网格信息
  - step(input) → 调用 swap_step 进行单日/单步模拟，返回输出
  - close() → 调用 swap_close 做收尾
- 错误处理：禁止Fortran直接终止进程；通过 `fromswap%ierrorcode` 统一回传错误码与日志内容，再在Cython层抛出Python异常。

数据流简图：
Python(用户) → Cython(封装) → C桥接(头/源) → Fortran(ISO C绑定) → SWAP核心

## Required Components（文件/组件清单）

1) Fortran 层（新增）
- swap420/swap_c_api.f90（新）：
  - 用ISO C Binding定义C可见的`type, bind(C)`结构体：`c_swap_input`，`c_swap_output`
  - 暴露 `bind(C)` 的入口：
    - `subroutine swap_c_init(ierr) bind(C,name="swap_c_init")`
    - `subroutine swap_c_step(inp, outp, ierr) bind(C,name="swap_c_step")`
    - `subroutine swap_c_close(ierr) bind(C,name="swap_c_close")`
  - 内部调用现有 `swap(iCaller,iTask,...)`（iCaller≠0）并做Fortran<->C结构体拷贝
  - 屏蔽/改造潜在的 `fatalerr` 终止流程，确保以错误码返回

2) C 头文件（新增）
- include/swap_c_api.h（新）：
  - `typedef struct { ... } c_swap_input;`
  - `typedef struct { ... } c_swap_output;`
  - `int swap_c_init(void);`
  - `int swap_c_step(const c_swap_input* inp, c_swap_output* outp);`
  - `int swap_c_close(void);`

3) Cython 层（新增）
- pyswap/_core.pxd（新）：声明cdef extern from "swap_c_api.h" 的C接口
- pyswap/_core.pyx（新）：
  - 将C结构体映射到Python类/字典；将 `outp.dz/wc/rwu` 暴露为numpy数组（零拷贝或复制）
  - 定义 `class SwapSession`：
    - `def __cinit__(self): ...`
    - `def init(self) -> None`
    - `def step(self, inp: dict) -> dict`
    - `def close(self) -> None`
  - 错误码非零→抛出 `SwapError`

4) Python 包装（新增）
- pyswap/__init__.py（新）：导出高级API
- pyswap/exceptions.py（新）：定义 `SwapError/ConvergenceError/BoundaryError` 等
- tests/ 目录（新）：pytest用例；最小E2E回归

5) 构建配置
- setup.py（或pyproject.toml）
  - Extension(name="pyswap._core", sources=[Fortran + Cython + 可选C桥接], extra_compile_args, libraries)
  - Windows下优先使用gfortran（MinGW-w64）或 Intel oneAPI；Linux/macOS使用gfortran
- setup.cfg / MANIFEST.in（可选）

6) 校验文件
- Cython 编译检验：pyswap/_core.pyx 的最小导入与 `swap_c_init()` 是否可调用

## 分步实施计划

步骤A：确定Fortran↔C接口（ISO C Binding）
- A1. 明确类型字段（参考 swap420/swap.f90 的 `type(swap_input)` 与 `type(swap_output)`）：
  - 输入：8字节浮点 tstart,tend,tmin,tmax,hum,wind,rain,wet,etref,rad；8字节浮点 ch,zroot,lai；8字节整数 icrop
  - 输出：8字节浮点 tstart,tend,tpot,tact；8字节整数 numnodes,ierrorcode；定长 real(8) 数组 dz/wc/rwu(500)
- A2. 在 swap420/swap_c_api.f90 中定义 bind(C) 结构体：
  - 注意：Fortran `integer(8)` 对应C的 `long long` 或 `int64_t`；`real(8)` 对应 `double`
  - 固定数组大小500与SWAP一致，避免可分配数组跨语言复杂性
- A3. 暴露三个 `bind(C)` 入口：
  - swap_c_init: 调用 `swap(iCaller=1, iTask=1)` 完成初始化；如有错误，将 ierrorcode 写入本地状态，并以 `ierr!=0` 返回
  - swap_c_step: 
    - 将 `c_swap_input` 拷贝到本地 Fortran `type(swap_input)` 变量
    - 调用 `swap(iCaller=1, iTask=2, toswap=..., fromswap=...)`
    - 拷贝 `fromswap` 到 `c_swap_output` 并返回ierr
  - swap_c_close:
    - 调用 `swap(iCaller=1, iTask=3)` 并以ierr返回
- A4. 错误处理：
  - `swap()` 内部已通过 `handle_exchange()` 设置 `fromswap%ierrorcode`；我们将所有非0视作错误
  - 对源码中调用 `fatalerr()` 的路径：不在此阶段大改逻辑；保持SWAP内部行为，但尽量通过外层捕获ierrorcode；如果仍可能 stop/abort，后续第二阶段考虑在关键处改为设置ierrorcode而非终止

步骤B：编写C头文件与最小C桥
- B1. include/swap_c_api.h 中等价声明结构体与函数原型（保持与Fortran布局一致，按C ABI对齐）
- B2. 如编译器/平台需要，提供一个空实现的 .c 文件仅包含该头（有助于链接器与wheel构建）

步骤C：Cython封装
- C1. 在 pyswap/_core.pxd 中声明：
  - ctypedef struct c_swap_input {...}
  - ctypedef struct c_swap_output {...}
  - int swap_c_init()
  - int swap_c_step(const c_swap_input* inp, c_swap_output* outp)
  - int swap_c_close()
- C2. 在 pyswap/_core.pyx 实现：
  - 将Python dict→c_swap_input 的安全填充（默认值/类型检查/单位说明）
  - 创建numpy数组视图暴露 outp.dz/wc/rwu 的前 numnodes 部分；如需跨ABI安全，先复制到新数组
  - 错误码处理：非0→抛 `SwapError`；根据 ierrorcode→子类异常映射
  - Session状态机保证 init/step/close 的调用时序
- C3. pyswap/__init__.py：导出 SwapSession；文档字符串说明字段单位与要求（tstart/tend含义与“单日=两者相等”约束）

步骤D：构建过程
- D1. 依赖：Cython、numpy、Fortran编译器（gfortran/ifx/ifort）、C编译器
- D2. setup.py：
  - 使用 setuptools + numpy.get_include()
  - from setuptools.extension import Extension；from Cython.Build import cythonize
  - extra_compile_args：Fortran层 `-O3 -fPIC`（或 /O2 /MD for MSVC/ifx），定义平台宏
  - sources：
    - Fortran：swap420/*.f90 全部 + 新增 swap420/swap_c_api.f90
    - Cython：pyswap/_core.pyx
    - C：可选空.c
  - libraries：如有需要链接系统时间/数学库
- D3. 构建命令：
  - 开发：`python setup.py build_ext --inplace`
  - 发行：`pip wheel .` 生成whl
- D4. Windows注意事项：
  - 建议统一使用MSYS2/MinGW-w64 gfortran + msvc兼容层；或完整使用Intel oneAPI（ifx + icx）构建Fortran与C/C++，避免ABI冲突

步骤E：测试策略
- E1. 单元测试（pytest）：
  - 初始化：`SwapSession().init()` 不抛异常
  - 单日步：构造最小 c_swap_input（合理物理量），`step()` 返回 numnodes>0，数组长度匹配，`tpot>=0`
  - 边界情况：icrop切换、极端温度/降雨、雨时段wet=0→ierrorcode
- E2. 回归测试：选取一个实际项目输入文件夹（.swp等），对比 SWAP 原生运行输出与 Cython封装输出（tpot/tact/土层含水量差在阈值内）
- E3. 稳定性：连续多日循环 step，检查质量守恒与无崩溃

步骤F：数据类型转换与内存管理细节
- F1. 类型映射：
  - Fortran real(8) ↔ C double ↔ Cython double ↔ numpy.float64
  - Fortran integer(8) ↔ C long long/int64_t ↔ Cython long long ↔ numpy.int64
- F2. 数组：固定500长度；通过 `outp.numnodes` 截取有效部分
- F3. 内存所有权：
  - C接口参数均由调用方分配；Fortran内部不在C结构体上分配/释放
  - 不在C接口中持久保存指针（无野指针）
- F4. 字符串/路径：SWAP内部仍按原有文件系统读写（ReadSwap/SwapOutput等）；后续阶段可再暴露Python侧路径配置接口

步骤G：错误与异常
- G1. swap420内部错误常通过 `fromswap%ierrorcode` 传回（见 swap420/swap.f90: handle_exchange）
- G2. 在C接口返回 `ierr!=0`；Cython映射为 `SwapError` 及子类
- G3. 如遇Fortran `fatalerr()` 直接终止风险：第一阶段先观测；若出现，第二阶段在局部改造为设置 `fromswap%ierrorcode` 并 return

## 构建过程（命令/示例setup.py）

示例 setup.py 关键片段（伪代码，后续落地可提供完整文件）：

- 文件布局建议：
  - setup.py
  - pyswap/_core.pyx, _core.pxd, __init__.py, exceptions.py
  - include/swap_c_api.h
  - swap420/*.f90 + 新增 swap420/swap_c_api.f90

- 伪代码：

1) 生成扩展
- 使用 cythonize(Extension(...), language_level="3")
- sources 包含 Fortran 与 Cython 源
- include_dirs 包含 numpy.get_include() 与 include/
- extra_link_args 根据平台设置

2) 本地构建
- python -m pip install -U pip wheel setuptools cython numpy
- python setup.py build_ext --inplace

## 测试策略（细化）

- 用例1：最小流程
  - session = SwapSession(); session.init();
  - inp = dict(tstart=...相等..., rain=1.0, etref=0.0, tmin=15.0, tmax=25.0, hum=0.75, wind=5.0, wet=0.1, rad=1.0e4, icrop=0, lai=0.0, ch=0.0, zroot=0.0)
  - out = session.step(inp)
  - 断言 out["numnodes"]>0，len(out["dz"]) == out["numnodes"]

- 用例2：作物在/离散切换
- 用例3：异常路径（tend!=tstart，期望抛异常）
- 用例4：多日循环，检查质量守恒偏差阈值

## Dependencies

- 构建依赖：
  - Python: Cython>=3.0, numpy>=1.23, setuptools, wheel
  - 编译器：gfortran 或 Intel ifx/ifort；C/C++编译器（gcc/clang/msvc）
  - 平台工具：Windows可选MSYS2/MinGW-w64；Linux常规GNU工具链

- 运行依赖：
  - numpy

## 可行性、任务量与重难点分析

- 可行性：高。SWAP已提供DLL调用路径（iCaller≠0）且 `swap_input/output` 明确；`handle_exchange` 已约定通过 `ierrorcode` 反馈错误；非常适合做稳定的C绑定。
- 任务量（第一阶段，提供Python可用的最小封装）：
  - Fortran C API：1–2天（定义结构体与3个入口、数据拷贝与ierr传递）
  - C头与Cython初版：2–3天
  - setup.py与多平台编译打通：2–5天（Windows是主要成本）
  - 单元与回归测试：2–3天
  - 合计：大约1.5–2周有效工时
- 重难点：
  - Windows编译链的兼容性（Fortran/C/C++/Python的ABI匹配）
  - Fortran `fatalerr()` 潜在直接终止，需要定位并在必要处改造为错误码返回（第二阶段解决）
  - 数组内存视图的零拷贝与安全性权衡（首版可采用复制，后续优化）
  - I/O路径：现阶段仍由SWAP内部处理，未来若要彻底Python化需要较大改造

## 后续扩展（第二阶段建议）

- 替换 `fatalerr()` 为统一错误码并集中处理；或在关键模块增加 `bind(C)` 级别的错误回调
- 提供Python侧的项目文件解析与路径控制，使SWAP I/O完全由Python驱动
- 将 `numnodes` 数组改为动态分配，并在C层携带长度，避免固定500
- 暴露更多观测量/诊断量（如能量平衡、迭代统计），支持更丰富的测试

