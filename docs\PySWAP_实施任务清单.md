## PySWAP ISO C Binding + Cython 实施任务清单

本清单围绕 SWAP420 源码（特别是 swap420/swap.f90 中的 swap_input / swap_output 定义与三阶段调用模式 iTask=1/2/3）组织，采用“可编译、可测试、可回滚”的迭代策略。编译器统一使用 Intel oneAPI ifx/ifort，数组策略采用复制（更稳）。

约定与目标：
- C API：导出 swap_c_init / swap_c_step / swap_c_close，内部调用 swap(iCaller=1, iTask=1/2/3)
- 结构体：c_swap_input / c_swap_output 与 Fortran 类型一一对应，定长数组 500
- Cython：提供 SwapSession(init/step/close)；输出 dz/wc/rwu 为 numpy 数组（复制策略）
- 构建：setuptools + Cython + Intel Fortran
- 测试：pytest 覆盖初始化、单日步、异常路径、多日回归与稳定性

---

### 零、环境与工具链准备（先决条件）

任务E1：Intel oneAPI 环境校验（0.5天）
- 目标：确认 ifx/ifort 可用，已加载 oneAPI 环境
- 验收标准：`ifx --version` 或 `ifort -V` 正常输出；Windows 通过 oneAPI Command Prompt 运行
- 命令：
  - Windows：`"%ONEAPI_ROOT%\\setvars.bat" && ifx --version`
  - Linux：`source $ONEAPI_ROOT/setvars.sh && ifx --version`
- 前置依赖：无
- 回滚/调试：使用 `where ifx`/`which ifx` 检查 PATH；未安装则先安装 oneAPI Base + HPC Kit

任务E2：Python 构建环境（0.5天）
- 目标：安装构建依赖并确认版本
- 验收标准：`python -c "import Cython, numpy; print(Cython.__version__, numpy.__version__)"` 正常
- 命令：`python -m pip install -U pip wheel setuptools cython numpy pytest`
- 前置依赖：E1

任务E3：最小探针编译（0.5天）
- 目标：用 ifx 对单个 .f90 进行探针编译
- 验收标准：`ifx -c -O3 swap420/swap.f90` 成功（仅验证编译链环境）
- 前置依赖：E1
- 兼容性：确保在 64-bit Python 与 64-bit oneAPI 工具链环境中构建（ABI 匹配）


### 结构体与函数签名（明确）

- C 头文件 include/swap_c_api.h（目标定义）：
```
#ifdef __cplusplus
extern "C" {
#endif
#include <stdint.h>

typedef struct {
  double tstart, tend;
  double tmin, tmax, hum, wind, rain, wet, etref, rad;
  double ch, zroot, lai;
  int64_t icrop;
} c_swap_input;

typedef struct {
  double tstart, tend;
  int64_t numnodes;
  double tpot, tact;
  int64_t ierrorcode;
  double dz[500], wc[500], rwu[500];
} c_swap_output;

int swap_c_init(void);
int swap_c_step(const c_swap_input* inp, c_swap_output* outp);
int swap_c_close(void);
#ifdef __cplusplus
}
#endif
```
- Fortran ISO C Binding（swap420/swap_c_api.f90 关键片段）：
```
use iso_c_binding

type, bind(C) :: c_swap_input
  real(C_DOUBLE) :: tstart, tend
  real(C_DOUBLE) :: tmin, tmax, hum, wind, rain, wet, etref, rad
  real(C_DOUBLE) :: ch, zroot, lai
  integer(C_INT64_T) :: icrop
end type

type, bind(C) :: c_swap_output
  real(C_DOUBLE) :: tstart, tend
  integer(C_INT64_T) :: numnodes
  real(C_DOUBLE) :: tpot, tact
  integer(C_INT64_T) :: ierrorcode
  real(C_DOUBLE) :: dz(500), wc(500), rwu(500)
end type

! bind(C) 入口（name 如下）
subroutine swap_c_init(ierr) bind(C, name="swap_c_init")
  use swap_exchange
  integer(C_INT) :: ierr
  ! 调用 swap(1,1)
end subroutine
```
- 数组复制策略：step 返回时仅复制前 numnodes 元素到 Python，超出部分保持为 0；避免共享内存与生命周期问题。


### 一、Fortran 层

任务F1：建立 ISO C Binding C API（1–2天）
- 目标：在 Fortran 层定义 C 可见结构体与 3 个 bind(C) 入口，完成与 swap() 的参数拷贝与调用
- 产出文件：
  - swap420/swap_c_api.f90（新）
- 主要内容：
  - 结构体字段对齐：
    - 输入 c_swap_input（与 swap_input 对齐）：
      - double tstart, tend, tmin, tmax, hum, wind, rain, wet, etref, rad
      - double ch, zroot, lai
      - int64_t icrop
    - 输出 c_swap_output（与 swap_output 对齐）：
      - double tstart, tend, tpot, tact
      - int64_t numnodes, ierrorcode
      - double dz[500], wc[500], rwu[500]
  - 函数：
    - bind(C,name="swap_c_init")   → 调用 swap(1,1)
    - bind(C,name="swap_c_step")   → 拷贝 inp→toswap，调用 swap(1,2,toswap,fromswap)，再拷贝 fromswap→outp
    - bind(C,name="swap_c_close")  → 调用 swap(1,3)
  - 数组复制策略：逐元素复制（避免共享内存/生命周期问题）
- 验收标准：
  - ifx/ifort 能编译通过全部 Fortran 源（含新文件）
  - 链接后导出符号存在（nm/dumpbin 可查）
- 前置依赖：无（基于现有 swap420/*.f90）
- 命令示例：
  - Windows(oneAPI 环境中)：`ifx /c /O3 swap420\*.f90`
  - Linux：`ifx -c -O3 swap420/*.f90`
- 应急方案：若遇 fatalerr 直接终止，先记录触发位置；暂以 ierrorcode 检测返回，二期再改造

任务F2：与 swap_input/swap_output 字段对照测试（0.5–1天）
- 目标：确保结构体布局与 Fortran 类型完全一致
- 产出：临时 Fortran 测试子程序（可内嵌在 swap_c_api.f90）
- 验收标准：
  - 手动/打印比对字段与数组拷贝是否一致；numnodes 截断有效
- 前置依赖：F1
- 应急方案：如发现不一致，优先在 C 头与 Cython 同步调整，保持 Fortran 源为准


任务F3（可选）：fatalerr 拦截与 ierrorcode 化（2–3天）
- 目标：对易触发的 fatalerr 路径进行本地化拦截，避免 Fortran 直接终止进程；改为设置 fromswap%ierrorcode 并 return
- 范围建议：swap.f90 中 handle_exchange 外的调用处（如 readmeteo.f90、timecontrol.f90、surfacewater.f90 等）按频次优先
- 产出：
  - 最小改造补丁（逐文件注释说明）；新增统一错误码清单（docs/ErrorCodes.md，可选）
- 实现要点：
  - 在可能处以条件编译或宏封装 fatalerr，例如：当 iCaller==1（DLL模式）时将消息写入日志并设置全局错误码变量；由 swap() 读入并回传 ierrorcode
  - 确保原生命令行模式（iCaller==0）行为不变
- 验收标准：
  - 能复现原先会崩溃的路径改为通过 ierrorcode 返回，不中断 Python 进程
  - 新增回归用例覆盖此路径（例如构造 tend!=tstart、或非法配置组合）
- 前置依赖：F1（完成基本 C API），可与 F2 并行
- 应急方案：如改造范围较大，优先在最常见触发点实现拦截，并在文档中列出其余路径的后续计划


附：F3 宏封装草稿（仅作说明，实际以源码文件结构为准）
```
! 在一个公共 include 或模块中定义：
logical :: g_dll_mode
integer :: g_last_error

subroutine set_dll_mode(flag)
  implicit none
  logical, intent(in) :: flag
  g_dll_mode = flag
end subroutine

subroutine fatalerr_guard(modname, message)
  implicit none
  character(*), intent(in) :: modname, message
  if (g_dll_mode) then
     g_last_error = 1001  ! 映射为统一错误码，或按上下文设置
     ! 可写入 logf：write(logf,'(A,1X,A)') trim(modname), trim(message)
     return
  else
     call fatalerr(modname, message)
  end if
end subroutine
```
- 使用方式：在 swap() 入口按 iCaller==1 调用 set_dll_mode(.true.)；在各处将 `call fatalerr(...)` 替换为 `call fatalerr_guard(...)`（分阶段推进）。
- 在 handle_exchange 末尾读取 g_last_error 并映射到 fromswap%ierrorcode。

---

### 二、C 层

任务C1：C 头文件定义（0.5天）
- 目标：声明与 Fortran 对齐的结构体与函数原型
- 产出文件：
  - include/swap_c_api.h（新）
- 内容要点：
  - `#include <stdint.h>`；`extern "C"` 支持 C++
  - typedef struct c_swap_input {...}; typedef struct c_swap_output {...};
  - `int swap_c_init(void);`
  - `int swap_c_step(const c_swap_input* inp, c_swap_output* outp);`
  - `int swap_c_close(void);`
- 验收标准：
  - 头文件可被 Cython 与编译器正常包含，无对齐告警
- 前置依赖：F1 字段定义定稿
- 应急方案：字段名或类型冲突时，以 Fortran 源为准修改头文件

任务C2：导出符号与链接验证（0.5天）
- 目标：保证 C 名字修饰与 Fortran 导出一致
- 产出：无（验证性任务）
- 验收标准：
  - Windows：dumpbin /exports 显示 swap_c_* 符号
  - Linux：nm -gU 显示符号；可写一个最小 C main 进行链接验证

- 前置依赖：F1、C1
- 应急方案：若符号名不一致，检查 bind(C,name=...) 与编译器修饰规则


任务T0：测试数据与工作目录准备（0.5–1天）
- 目标：提供最小 SWAP 项目数据集，并在测试中设置工作目录，确保 ReadSwap 能找到输入
- 产出：
  - tests/data/min_project/（包含 swap.swp 及其依赖的最小配置文件）
- 验收标准：
  - 在 tests 中 `os.chdir('tests/data/min_project')` 后，`SwapSession().init()` 能成功
- 前置依赖：E2、B1（可在构建后运行）
- 应急方案：如缺少真实项目文件，先使用存根/简化输入，并在 CI 中标记为“可选回归”

任务B3（可选）：Fortran 静态库构建与链接（1天）
- 目标：先用 ifx 生成静态库 libswapcore.lib，再由 setup.py 仅编译 Cython 并链接该库，提升构建稳定性
- 产出：build/libswapcore.lib（或项目根目录下 libswapcore.lib）
- 步骤：
  - `call "%ONEAPI_ROOT%\setvars.bat"`
  - `ifx /c /O3 swap420\*.f90` 生成一批 .obj
  - `lib /OUT:libswapcore.lib *.obj`
  - 在 setup.py 的 Extension 中改为 sources=["pyswap/_core.pyx"], libraries=["swapcore"], library_dirs=["."]
- 验收标准：
  - `python setup.py build_ext --inplace` 成功，pyd 链接到 libswapcore.lib
- 前置依赖：B1
- 应急方案：若链接失败，回退到直接编译全部 .f90 的方案

- 前置依赖：F1、C1
- 应急方案：若符号名不一致，检查 bind(C,name=...) 与编译器修饰规则

---

### 三、Cython 层

任务Y1：PXD/基础声明（0.5天）
- 目标：在 .pxd 中声明 C 结构体与函数
- 产出文件：
  - pyswap/_core.pxd（新）
- 内容要点：
  - `cdef extern from "swap_c_api.h":` 声明 c_swap_input/c_swap_output/swap_c_*
- 验收标准：
  - cythonize 通过（语法/声明无误）
- 前置依赖：C1

### 四、Python 层

任务P1：异常体系与API导出（0.5天）
- 目标：定义异常类与对外 API
- 产出文件：
  - pyswap/exceptions.py（新）：SwapError, ConvergenceError, BoundaryError
  - pyswap/__init__.py（新）：from ._core import SwapSession
- 验收标准：
  - `from pyswap import SwapSession` 可用
- 前置依赖：Y2

任务P2：用户文档与示例（0.5–1天）
- 目标：提供最小使用例与字段说明

附：CI1 最小 ci.yml 草稿（Windows + oneAPI）
```
name: ci
on:
  push:
  pull_request:
jobs:
  build-test:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
      - name: Install Intel oneAPI base + hpc (缓存/私有镜像可优化)
        run: |
          echo Skipping real install here; please add oneAPI offline installer step
      - name: Build
        shell: cmd
        run: |
          call "%ONEAPI_ROOT%\setvars.bat"
          python -m pip install -U pip wheel setuptools cython numpy pytest
          python setup.py build_ext --inplace
      - name: Test
        shell: cmd
        run: |
          pytest -q
```
- 注意：oneAPI 安装步骤需按你环境补全（可用缓存或自建 runner）。

- 产出文件：
  - docs/PySWAP_使用示例.md（新）
- 内容：
  - 最小示例：init → step（单日）→ close
  - 字段单位与约束（tstart/tend 时间基准、单位 mm/d、kJ/m²/d 等）
- 验收标准：
  - 示例可复制运行（在已构建的环境中）
- 前置依赖：P1

---

### 五、构建配置

任务B1：setup.py（Intel oneAPI 配置）（1–2天）
- 目标：使用 ifx/ifort 构建 Fortran + Cython 扩展
- 产出文件：

任务CI1（可选）：GitHub Actions（Windows+oneAPI）构建与测试（1–2天）
- 目标：在 CI 上验证 ifx 构建扩展与基础 pytest 用例
- 产出：.github/workflows/ci.yml（新）
- 实现要点：
  - 使用 windows-latest runner；预先下载并安装 Intel oneAPI（或使用已配置 runner）
  - 步骤：checkout → 配置 oneAPI → 安装 Python 依赖 → build_ext --inplace → pytest -q
  - 缓存 wheel/构建工件以提速
- 验收标准：
  - CI 成功运行，输出缓存工件，pytest 通过
- 前置依赖：B1、T1（或最小 smoke test）
- 应急方案：若 oneAPI 安装耗时或受限，可改为本地 CI 或仅做 lint + cythonize 测试

  - setup.py（新或修改）
- 关键细节：
  - 检测 oneAPI 环境（IFX/IFORT 可执行）
  - Fortran 编译器指定 ifx/ifort；优化开关 -O3；Windows 使用 /MD
  - sources：swap420/*.f90 + swap420/swap_c_api.f90 + pyswap/_core.pyx
  - include_dirs：numpy.get_include() 与 include/
- 验收标准：
  - `python -m pip install -U pip wheel setuptools cython numpy`
  - `python setup.py build_ext --inplace` 成功
- 前置依赖：F1、Y1
- 应急方案：如遇工具链冲突，提供备用 make/nmake 脚本或改用 numpy.distutils（过渡）

任务B2：Wheel 打包与平台校验（0.5–1天）
- 目标：生成并安装 wheel，验证导入与简单调用
- 产出：
  - dist/*.whl
- 验收标准：
  - `pip wheel .` 成功，`pip install dist/*.whl` 后能导入与调用 init()
- 前置依赖：B1

---

### 六、测试验证

任务T1：单元测试（1天）
- 目标：基础行为验证
- 产出文件：
  - tests/test_basic.py（新）
- 用例：
  - 初始化：SwapSession().init()
  - 单日：step() 返回 numnodes>0，dz/wc/rwu 长度与 numnodes 一致
  - 异常：tend!=tstart → 抛 SwapError
- 验收标准：
  - `pytest -q` 通过
- 前置依赖：P1、B1

任务T2：回归与稳定性（1–2天）
- 目标：与原生 SWAP 输出对齐、长序列稳定
- 产出文件：
  - tests/test_regression.py（新）
- 内容：
  - 选择样本项目，比较 tpot/tact/含水量与原生输出，阈值内通过
  - 多日循环 step 匹配质量守恒阈值
- 验收标准：
  - `pytest -q` 通过
- 前置依赖：T1
- 应急方案：阈值调整、记录差异，定位数值差异来源

---

### 七、示例数据集集成：hupselbrook（txt→Python输入映射）

任务HP0：工作目录选择（0.2天）
- 目标：在初始化前将工作目录切换到示例数据目录，便于 ReadSwap 定位 swap.swp 与关联文件
- 产出：无（测试脚本中的 cwd 切换）
- 步骤：`os.chdir(r'D:\code\Python_item\PySWAP\hupselbrook')`

任务HP4 实现草稿（可选）：grassd.crp 简易映射器（1–2天）
- 目标：读取 grassd.crp，基于 DNR 与 CH 表，生成每日 icrop/lai/ch/zroot 的近似曲线
- 简化规则（建议）：
  - icrop：在 TSTART–TEND 全年内为 1；可为冬季设短暂停滞（可选）
  - ch：按 Part 1 列表 DNR-CH 做线性插值到每日；单位 cm
  - lai：以 LAIEM + RGRLAI 线性生长至一个上限（例如 2.0），随后保持（或结合 SLA/AMAX 表做更精细）
  - zroot：固定或缓慢线性增长至 25–40 cm（示例）；后续可结合土壤与作物参数优化
- 产出：
  - utils/grass_crp_mapper.py（新），暴露 iter_grass_daily(tstart, tend) 生成器
- 验收标准：
  - HP3 中启用该映射器，能稳定运行 30 天；icrop=1 时 LAI/CH/RD 合理（非负、数量级适当）
- 前置依赖：HP3
- 应急方案：如解析 .crp 成本较高，先基于常量或线性规则提供可运行基线

附：HP4 映射器骨架（示例）
```
from datetime import date, timedelta

def lin_interp(points, x):
    # points: list of (x, y) sorted by x, linear interpolate
    if x <= points[0][0]:
        return points[0][1]
    for i in range(1, len(points)):
        if x <= points[i][0]:
            x0,y0 = points[i-1]
            x1,y1 = points[i]
            t = (x - x0)/float(x1-x0) if x1!=x0 else 0.0
            return y0 + t*(y1-y0)
    return points[-1][1]

def iter_grass_daily(tstart_daynum, tend_daynum, lai_em=0.63, rgr_lai=0.007, lai_cap=2.0,
                     ch_points=((0.0,12.0),(180.0,12.0),(366.0,12.0)), zroot0=25.0, zroot1=40.0):
    # daynum counted from Jan 1, same as SWAP DNR within a year
    span = int(tend_daynum - tstart_daynum) + 1
    lai = lai_em
    for k in range(span):
        dnr = (k % 366)
        ch = lin_interp(ch_points, float(dnr))
        # naive LAI growth
        if lai < lai_cap:
            lai = min(lai_cap, lai + rgr_lai)
        # naive zroot growth
        zroot = zroot0 + (zroot1 - zroot0) * min(1.0, k/180.0)
        yield dict(icrop=1, lai=float(lai), ch=float(ch), zroot=float(zroot))
```

- 验收标准：`SwapSession().init()` 成功，无找不到文件错误
- 前置依赖：E2、B1

任务HP1：读取 swap.swp，解析窗口、气象文件与约束（0.5天）
- 目标：提取 TSTART/TEND、METFIL，并解析 SWETR/SWDIVIDE/SWMETDETAIL/SWRAIN 约束
- 产出：解析函数或脚本（可驻留 tests/utils 或 examples/）
- 验收标准：
  - 解析到 TSTART=2002-01-01、TEND=2004-12-31、METFIL='283.met'
  - 解析到 SWETR=0、SWDIVIDE=1、SWMETDETAIL=0、SWRAIN=0（与 handle_exchange(11) 兼容）
- 前置依赖：HP0

任务HP2：将 283.met 转换为 Python 封装所需的 per-day 输入（1天）
- 目标：从 283.met 中逐行构造 step() 的输入字典
- 映射关系：
  - rad(kJ/m²/d)→rad；tmin/tmax(°C)→tmin/tmax；hum(kPa)→hum；wind(m/s)→wind
  - rain(mm/d)→rain；etref(mm/d)→etref；wet(0..1)→wet
  - icrop=0，lai=0.0，ch=0.0，zroot=0.0（首版无需解析 .crp）
  - tstart/tend：由 YYYY,MM,DD 换算为 days since 1900-01-01；且单日步满足 tstart==tend
- 产出：
  - 一个 Python 生成器或列表输入：List[Dict[str, Any]]
- 验收标准：
  - 第一条记录字段与文件行一致（例如 2002-01-01：rad=3810.0, rain=0.0, etref=0.4, wet=0.0 等）
- 代码示例：
```
from datetime import date
import csv, os

BASE = date(1900,1,1)

def day_number(y,m,d):
    return (date(y,m,d) - BASE).days

def iter_hupsel_met(met_path):
    with open(met_path, newline='', encoding='utf-8') as f:
        reader = csv.reader(f)
        header_found = False
        for row in reader:
            if not row or row[0].startswith('*'):
                continue
            if not header_found:
                # Expect header row
                header_found = True
                continue
            # Station,DD,MM,YYYY,Rad,Tmin,Tmax,Hum,Wind,Rain,ETref,Wet
            _, dd, mm, yyyy, rad, tmin, tmax, hum, wind, rain, etref, wet = row



            y, m, d = int(yyyy), int(mm), int(dd)
            t = float(day_number(y,m,d))
            yield dict(
                tstart=t, tend=t,
                rad=float(rad), tmin=float(tmin), tmax=float(tmax), hum=float(hum), wind=float(wind),
                rain=float(rain), etref=float(etref), wet=float(wet),
                icrop=0, lai=0.0, ch=0.0, zroot=0.0,
            )

# 用法示例（需先 chdir 到 hupselbrook）
# for d in iter_hupsel_met('283.met'): print(d)
```
- 前置依赖：HP1

任务HP3：端到端演示（1天）
- 目标：使用 SwapSession 对 HP2 生成的每日输入序列执行循环 step
- 产出：examples/hupselbrook_demo.py（或 tests/test_hupsel_demo.py）
- 步骤：
  1) `os.chdir('.../hupselbrook')`; 2) `s=SwapSession(); s.init()`
  3) 遍历 `iter_hupsel_met('283.met')`，逐日 `s.step(d)`；记录若干输出（tpot/tact/numnodes）
  4) s.close()
- 验收标准：
  - 前7天运行不崩溃，numnodes>0，tpot/tact 为非负实数
- 前置依赖：Y2、P1、HP2

任务HP4（可选）：作物文件（*.crp）到 icrop/lai/ch/zroot 的映射（2天）
- 目标：解析 grassd/maizes/potatod/result.crp，得到简单的作物在场/高度/根深/LAI 的日历
- 产出：
  - 可将 icrop=1 的生长窗口推导出；用经验或 .crp 参数近似 LAI/CH/RD
- 验收标准：
  - 在 HP3 中启用 icrop=1 的时段与 LAI/CH/RD，运行稳定
- 前置依赖：HP3


任务Y2：核心封装类实现（1–2天）
- 目标：实现 SwapSession（init/step/close），数组复制输出
- 产出文件：
  - pyswap/_core.pyx（新）
- 关键细节：
  - Python dict → c_swap_input 填充时类型/单位检查（tstart==tend）
  - 调用 swap_c_step 后，将 outp.dz/wc/rwu[:numnodes] 复制到 numpy.ndarray
  - 错误映射：ierr!=0 或 outp.ierrorcode!=0 → 抛出 exceptions.SwapError 子类
- 验收标准：
  - `python setup.py build_ext --inplace` 编译通过
  - `from pyswap import SwapSession` 可导入；`SwapSession().init()` 可正常调用（运行时不崩溃）
- 前置依赖：Y1、构建任务部分完成

### 各任务子任务明细（Windows 实施）

- 任务E1 子任务：
  1) 打开 “Intel oneAPI Command Prompt for Intel 64” 或在普通CMD执行：`call "C:\\Program Files (x86)\\Intel\\oneAPI\\setvars.bat"`
  2) 执行 `ifx --version` 与 `ifort -V`，记录版本号
  3) 执行 `where ifx` 查看路径，确认 PATH 已包含 oneAPI 二进制目录

- 任务E2 子任务：
  1) `python -m venv .venv && .venv\\Scripts\\activate`
  2) `python -m pip install -U pip wheel setuptools cython numpy pytest`
  3) 验证：`python -c "import Cython, numpy; print(Cython.__version__, numpy.__version__)"`

- 任务E3 子任务：
  1) 探针编译 trivial.f90：创建临时文件 `print *, 'ok'`
  2) `ifx /c /O2 trivial.f90` → 生成 trivial.obj 视为通过；删除临时文件
  3) 如需直接验证 SWAP 单元：`ifx /c /O2 swap420\\swap.f90`（可能因依赖报错，属预期）

- 任务F1 子任务：
  1) 在 swap420/ 新建 swap_c_api.f90，导入 `use iso_c_binding` 与 `use swap_exchange`
  2) 定义 `type, bind(C) :: c_swap_input` 与 `c_swap_output`（字段与固定数组500）
  3) 实现 `swap_c_init/step/close`：内部调用 `swap(1,1/2/3, ...)`
  4) step 内部：将 C 输入拷贝到 `type(swap_input)`，调用后将 `fromswap` 拷贝到 C 输出（仅前 numnodes）
  5) 返回码约定：`ierr = 0` 表示成功，非0为 `fromswap%ierrorcode`

- 任务F2 子任务：
  1) 在 `swap_c_api.f90` 添加临时测试例程（条件编译）打印 c↔Fortran 字段
  2) 使用 `ifx /c /O2 swap420\\*.f90` 编译；检查无布局/类型告警
  3) 移除/禁用临时测试代码

- 任务C1 子任务：
  1) 新建 include/ 目录与 `swap_c_api.h`
  2) 填写结构体、函数原型与 `extern "C"` 包装
  3) 在 Cython pxd/pyx 中验证能正常 `#include`（通过 cythonize）

- 任务C2 子任务：
  1) 构建后用 `dumpbin /exports <生成的pyd或dll>` 检查 `swap_c_*` 符号（或 `dumpbin /symbols` 目标对象）
  2) 如需要，编写最小 C 程序调用 `swap_c_init()` 并与对象/库链接验证

- 任务Y1 子任务：
  1) 新建 pyswap/_core.pxd，声明 c 结构体与 `int swap_c_*` 函数
  2) 确认包含路径在 setup.py 配置（include/ 与 numpy include）
  3) 运行 cythonize 进行语法检查

- 任务Y2 子任务：
  1) 新建 pyswap/_core.pyx，`cdef class SwapSession` 定义 cdef 成员（状态位）
  2) 实现 `init()`：调用 `swap_c_init()`，错误→抛 `SwapError`
  3) 实现 `step(inp: dict)`：校验/填充 c_swap_input；调用 `swap_c_step()`；将 outp 数组[:numnodes] 复制到 numpy.ndarray；错误映射
  4) 实现 `close()`：调用 `swap_c_close()`；确保可重复调用安全
  5) 导入/使用 numpy：`import numpy as np; cimport numpy as cnp` 并设置 dtype=float64

- 任务P1 子任务：
  1) 新建 pyswap/exceptions.py：定义 `SwapError` 及子类
  2) 新建 pyswap/__init__.py：导出 `SwapSession`
  3) 在 docstring 中说明字段单位与 tstart==tend 约束

- 任务P2 子任务：
  1) 新建 docs/PySWAP_使用示例.md，给出最小可运行代码（Windows 命令行）
  2) 示例包含：环境激活→构建→导入→init→step→close

- 任务B1 子任务（Windows/ifx）：
  1) 在 setup.py 中设置：
     - 检测 oneAPI 环境：`os.environ.get('ONEAPI_ROOT')`
     - Fortran 编译器指定 ifx：可通过 `os.environ['FC']='ifx'`
     - include_dirs：`numpy.get_include()` 与 `include/`
     - sources：`glob('swap420/*.f90') + ['swap420/swap_c_api.f90', 'pyswap/_core.pyx']`
     - extra_link_args：`['/MD']`（与 Python CRT 匹配）
  2) 构建命令：`call "%ONEAPI_ROOT%\\setvars.bat" && python setup.py build_ext --inplace`
  3) 产物验证：生成 `pyswap\\_core.*.pyd`

- 任务B2 子任务：
  1) 生成 wheel：`call "%ONEAPI_ROOT%\\setvars.bat" && pip wheel .`
  2) 安装 wheel：`pip install dist\\*.whl`；校验 `python -c "from pyswap import SwapSession; print('ok')"`

- 任务T1 子任务：
  1) 新建 tests/test_basic.py：
     - 测试 init：不抛异常
     - 测试 step：构造单日输入，断言 `numnodes>0`、数组长度与 `numnodes` 一致
     - 测试异常：tend!=tstart → 抛 `SwapError`
  2) 运行：`pytest -q`

- 任务T2 子任务：
  1) 准备样本项目数据目录（若有）：对齐 tpot/tact/含水量
  2) 多日循环 step，记录质量守恒偏差，阈值内通过
  3) 运行：`pytest -q`

- 应急方案：若导入失败，优先在 setup.py 调整编译器与 include 路径

---

### 附录A：Fortran ISO C Binding 示例骨架（swap420/swap_c_api.f90）

```
module swap_c_api_mod
  use iso_c_binding
  use swap_exchange
  implicit none

  type, bind(C) :: c_swap_input
    real(C_DOUBLE)       :: tstart, tend
    real(C_DOUBLE)       :: tmin, tmax, hum, wind, rain, wet, etref, rad
    real(C_DOUBLE)       :: ch, zroot, lai
    integer(C_INT64_T)   :: icrop
  end type

  type, bind(C) :: c_swap_output
    real(C_DOUBLE)       :: tstart, tend
    integer(C_INT64_T)   :: numnodes
    real(C_DOUBLE)       :: tpot, tact
    integer(C_INT64_T)   :: ierrorcode
    real(C_DOUBLE)       :: dz(500), wc(500), rwu(500)
  end type

contains

  subroutine swap_c_init(ierr) bind(C, name="swap_c_init")
    use iso_c_binding
    implicit none
    integer(C_INT), intent(out) :: ierr
    type(swap_output) :: outp
    ierr = 0
    call swap(1, 1)
  end subroutine

  subroutine swap_c_step(inp, outp_c, ierr) bind(C, name="swap_c_step")
    use iso_c_binding
    implicit none
    type(c_swap_input),  intent(in)  :: inp
    type(c_swap_output), intent(out) :: outp_c
    integer(C_INT),      intent(out) :: ierr
    type(swap_input)  :: fin
    type(swap_output) :: fout
    integer :: i

    ierr = 0
    ! copy C -> Fortran input
    fin%tstart = inp%tstart; fin%tend = inp%tend
    fin%tmin = inp%tmin; fin%tmax = inp%tmax; fin%hum = inp%hum
    fin%wind = inp%wind; fin%rain = inp%rain; fin%wet = inp%wet
    fin%etref = inp%etref; fin%rad = inp%rad
    fin%ch = inp%ch; fin%zroot = inp%zroot; fin%lai = inp%lai
    fin%icrop = int(inp%icrop, kind(fout%ierrorcode))

    call swap(1, 2, toswap=fin, fromswap=fout)

    ! copy Fortran -> C output (truncate by numnodes)
    outp_c%tstart = fout%tstart; outp_c%tend = fout%tend
    outp_c%numnodes = fout%numnodes
    outp_c%tpot = fout%tpot; outp_c%tact = fout%tact
    outp_c%ierrorcode = fout%ierrorcode
    do i = 1, 500
      if (i <= fout%numnodes) then
        outp_c%dz(i)  = fout%dz(i)
        outp_c%wc(i)  = fout%wc(i)
        outp_c%rwu(i) = fout%rwu(i)
      else
        outp_c%dz(i)  = 0.0d0
        outp_c%wc(i)  = 0.0d0
        outp_c%rwu(i) = 0.0d0
      end if
    end do

    if (fout%ierrorcode /= 0) ierr = int(fout%ierrorcode, C_INT)
  end subroutine

  subroutine swap_c_close(ierr) bind(C, name="swap_c_close")
    use iso_c_binding
    implicit none
    integer(C_INT), intent(out) :: ierr
    ierr = 0
    call swap(1, 3)
  end subroutine

end module
```

注意：此为骨架示例，实际以项目编译通过为准（例如模块名/依赖 use 列表可能需调整）。

### 附录B：C 头文件示例（include/swap_c_api.h）

```
#ifndef SWAP_C_API_H
#define SWAP_C_API_H
#ifdef __cplusplus
extern "C" {
#endif
#include <stdint.h>

typedef struct {
  double tstart, tend;
  double tmin, tmax, hum, wind, rain, wet, etref, rad;
  double ch, zroot, lai;
  int64_t icrop;
} c_swap_input;

typedef struct {
  double tstart, tend;
  int64_t numnodes;
  double tpot, tact;
  int64_t ierrorcode;
  double dz[500], wc[500], rwu[500];
} c_swap_output;

int swap_c_init(void);
int swap_c_step(const c_swap_input* inp, c_swap_output* outp);
int swap_c_close(void);

#ifdef __cplusplus
}
#endif
#endif
```

### 附录C：Cython 骨架（pyswap/_core.pxd 与 _core.pyx）

_pxd_
```
cdef extern from "swap_c_api.h":
    cdef struct c_swap_input:
        double tstart, tend
        double tmin, tmax, hum, wind, rain, wet, etref, rad
        double ch, zroot, lai
        long long icrop
    cdef struct c_swap_output:
        double tstart, tend
        long long numnodes
        double tpot, tact
        long long ierrorcode
        double dz[500]
        double wc[500]
        double rwu[500]
    int swap_c_init()
    int swap_c_step(const c_swap_input* inp, c_swap_output* outp)
    int swap_c_close()
```

_pyx_
```
# distutils: language = c
# cython: language_level=3
import numpy as np
cimport numpy as cnp
from .exceptions import SwapError
cimport pyswap._core as ccore  # 自引以便类型引用（或从 pxd 直接 import）

cdef class SwapSession:
    cdef bint _inited
    def __cinit__(self):
        self._inited = False
    def init(self):
        cdef int ierr = swap_c_init()
        if ierr != 0:
            raise SwapError(f"swap_c_init failed: {ierr}")
        self._inited = True
    def step(self, dict inp):
        if not self._inited:
            raise SwapError("Call init() first")
        cdef c_swap_input cinp
        cdef c_swap_output cout
        # TODO: 从 inp 填充 cinp（含 tstart==tend 检查）
        cdef int ierr = swap_c_step(&cinp, &cout)
        if ierr != 0 or cout.ierrorcode != 0:
            raise SwapError(f"swap_c_step failed: ierr={ierr}, ferr={cout.ierrorcode}")
        n = <int>cout.numnodes
        dz = np.array([cout.dz[i] for i in range(n)], dtype=np.float64)
        wc = np.array([cout.wc[i] for i in range(n)], dtype=np.float64)
        rwu = np.array([cout.rwu[i] for i in range(n)], dtype=np.float64)
        return dict(numnodes=n, tpot=cout.tpot, tact=cout.tact, dz=dz, wc=wc, rwu=rwu)
    def close(self):
        cdef int ierr = swap_c_close()
        if ierr != 0:
            raise SwapError(f"swap_c_close failed: {ierr}")
        self._inited = False
```

### 附录D：Windows 下 setup.py 片段（ifx）

```
from setuptools import setup, Extension
from Cython.Build import cythonize
import numpy as np, os, glob

os.environ.setdefault('FC', 'ifx')  # 强制使用 ifx

ext = Extension(
    name="pyswap._core",
    sources=glob.glob("swap420/*.f90") + [
        "swap420/swap_c_api.f90",
        "pyswap/_core.pyx",
    ],
    include_dirs=[np.get_include(), os.path.join(os.getcwd(), "include")],
    extra_link_args=["/MD"],
)

setup(
    name="pyswap",
    version="0.1.0",
    ext_modules=cythonize([ext], language_level=3),
)
```

### 附录E：时间编码约定（tstart/tend）
- tstart 与 tend 单位与约定来自 Fortran：days since 1900-01-01（见 swap.f90 中 dtdpar/dtdpst 用法）
- 单日步必须满足：tend == tstart；否则 handle_exchange 将置 ierrorcode=2
- Windows 注意本地化与小数点：Python 侧使用 float，并在 Cython/Fortran 层保持 IEEE 双精度传递，无需 locale 额外处理

### 附录F：最小使用示例（Windows）
```
REM 1) 载入 oneAPI 环境
call "%ONEAPI_ROOT%\setvars.bat"

REM 2) 安装依赖
python -m pip install -U pip wheel setuptools cython numpy pytest

REM 3) 本地构建
python setup.py build_ext --inplace

REM 4) 运行
python - <<PY
from pyswap import SwapSession
s = SwapSession()
s.init()
out = s.step(dict(tstart=693596.0, tend=693596.0, rain=1.0, etref=0.0,
                  tmin=15.0, tmax=25.0, hum=0.75, wind=5.0, wet=0.1, rad=1.0e4,
                  icrop=0, lai=0.0, ch=0.0, zroot=0.0))
print(out["numnodes"], out["tpot"], out["tact"])
s.close()
PY
```



### 常见问题与解决
- ifx/ifort 找不到：确保在 oneAPI 命令行环境中运行（已加载 setvars）
- 链接符号缺失：检查 bind(C,name=...) 与导出名；确认链接顺序与对象文件包含完全
- 数组越界：严格使用 outp.numnodes 截断；固定长度 500 内复制
- Fortran 直接终止：定位 fatalerr 调用点；二期改造为设置 ierrorcode 并返回

### 性能与稳定性验证
- 首版以复制为主，关注端到端吞吐与正确性
- 性能基准：记录日步耗时；后续再评估零拷贝或批量调用的收益
- 稳定性：长序列运行与内存占用监控（无泄漏、无异常增长）

---

### 任务依赖图（简述）
- F1 → F2 → C1 → C2 → Y1 → B1 → Y2 → P1 → B2 → T1 → P2 → T2

完成以上任务后，应可得到一个稳定的 pyswap._core 扩展模块与 SwapSession 高层API，支持以 Intel oneAPI ifx/ifort 构建、以数组复制策略进行稳健的数据交换，并通过基础的正确性与稳定性测试。
