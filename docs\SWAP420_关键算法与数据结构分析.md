# SWAP 4.2.0 关键算法与数据结构深度分析

## 1. 核心数据结构分析

### 1.1 全局状态管理 (variables.f90)

#### 1.1.1 关键数组结构
```fortran
! 土壤物理状态数组 (MACP = 5000)
real(8) :: theta(macp)      ! 体积含水量 [m³/m³]
real(8) :: h(macp)          ! 压力水头 [cm]  
real(8) :: k(macp)          ! 水力传导度 [cm/d]
real(8) :: q(macp+1)        ! 水流通量 [cm/d]
real(8) :: dz(macp)         ! 土层厚度 [cm]
real(8) :: z(macp)          ! 节点深度 [cm]

! 土壤层属性数组
integer :: layer(macp)      ! 节点所属土层编号
real(8) :: cofgen(25,macp)  ! 土壤水力参数矩阵
```

#### 1.1.2 时间控制变量
```fortran
real(8) :: t1900           ! 自1900年以来的天数
real(8) :: dt              ! 当前时间步长 [d]
real(8) :: dtmin, dtmax    ! 最小/最大时间步长 [d]
logical :: fldaystart      ! 新一天开始标志
logical :: flrunend        ! 模拟结束标志
```

#### 1.1.3 边界条件数组
```fortran
real(8) :: arai(366)       ! 日降雨量数组 [cm/d]
real(8) :: aetr(366)       ! 日参考蒸散发 [cm/d]
real(8) :: atmn(366)       ! 日最低温度 [°C]
real(8) :: atmx(366)       ! 日最高温度 [°C]
```

### 1.2 派生数据类型 (swap_exchange模块)

#### 1.2.1 输入数据结构
```fortran
type :: swap_input
    real(8) :: tstart, tend    ! 开始/结束时间 [days since 1900]
    real(8) :: tmin, tmax      ! 最低/最高温度 [°C]
    real(8) :: hum, wind       ! 湿度 [kPa], 风速 [m/s]
    real(8) :: rain, wet       ! 降雨 [mm/d], 湿润度 [-]
    real(8) :: etref, rad      ! 参考蒸散发 [mm/d], 辐射 [kJ/m²/d]
    real(8) :: ch, zroot, lai  ! 作物高度 [cm], 根深 [cm], 叶面积指数 [-]
    integer(8) :: icrop        ! 作物存在标志 [0/1]
end type
```

#### 1.2.2 输出数据结构
```fortran
type :: swap_output
    real(8) :: tstart, tend           ! 时间范围
    integer(8) :: numnodes            ! 土壤节点数
    real(8) :: tpot, tact            ! 潜在/实际蒸腾 [cm/d]
    integer(8) :: ierrorcode         ! 错误代码
    real(8), dimension(500) :: dz    ! 土层厚度数组 [cm]
    real(8), dimension(500) :: wc    ! 含水量数组 [m³/m³]
    real(8), dimension(500) :: rwu   ! 根系吸水数组 [cm/d]
end type
```

## 2. 核心算法分析

### 2.1 Richards方程数值求解

#### 2.1.1 控制方程
Richards方程的混合形式：
```
∂θ/∂t = ∂/∂z[K(θ)(∂h/∂z + 1)] - S(z,t)
```
其中：
- θ: 体积含水量 [m³/m³]
- h: 压力水头 [cm]
- K: 水力传导度 [cm/d]
- S: 源汇项 [d⁻¹]

#### 2.1.2 有限差分离散化 (soilwater.f90)
```fortran
! 时间离散：向后Euler格式
! 空间离散：中心差分格式
do node = 2, numnod-1
    ! 计算节点间水力传导度
    kup = hcomean(swkmean, k(node), k(node-1), dz(node), dz(node-1))
    klow = hcomean(swkmean, k(node), k(node+1), dz(node), dz(node+1))
    
    ! 构建三对角矩阵系数
    a(node) = -dt * kup / (dz(node) * dzsum_up)
    c(node) = -dt * klow / (dz(node) * dzsum_low)  
    b(node) = moiscap(node) + dt * (kup + klow) / dz(node)
    
    ! 右端项
    r(node) = theta_old(node) + dt * source(node)
end do
```

#### 2.1.3 非线性迭代求解
```fortran
! Picard迭代法
do iter = 1, maxiter
    ! 更新水力传导度和比水容量
    call update_hydraulic_properties()
    
    ! 求解线性化方程组
    call tridag(a, b, c, r, h_new, numnod)
    
    ! 检查收敛性
    if (convergence_check()) exit
    
    ! 更新迭代值
    h = h_new
end do
```

### 2.2 土壤水力特性函数

#### 2.2.1 Van Genuchten模型 (functions.f90)
```fortran
! 水分特征曲线
real(8) function watcon(node, head)
    thetar = cofgen(1,node)  ! 残余含水量
    thetas = cofgen(2,node)  ! 饱和含水量  
    alpha = cofgen(4,node)   ! 形状参数 α
    n = cofgen(6,node)       ! 形状参数 n
    m = cofgen(7,node)       ! m = 1 - 1/n
    
    if (head < 0.0d0) then
        se = 1.0d0 / (1.0d0 + (abs(alpha*head))**n)**m
        watcon = thetar + (thetas - thetar) * se
    else
        watcon = thetas
    endif
end function
```

#### 2.2.2 水力传导度函数
```fortran
real(8) function hconduc(node, theta)
    ! Mualem-van Genuchten模型
    ksat = cofgen(3,node)    ! 饱和水力传导度
    se = (theta - thetar) / (thetas - thetar)  ! 有效饱和度
    
    if (se > 0.0d0 .and. se < 1.0d0) then
        hconduc = ksat * se**0.5d0 * (1.0d0 - (1.0d0 - se**(1.0d0/m))**m)**2
    else if (se >= 1.0d0) then
        hconduc = ksat
    else
        hconduc = 0.0d0
    endif
end function
```

### 2.3 蒸散发计算算法

#### 2.3.1 Penman-Monteith方程 (penmon.f90)
```fortran
! 参考蒸散发计算
subroutine penmon(tmin, tmax, hum, wind, rad, alt, etref)
    ! 饱和水汽压
    es = 0.5d0 * (svp(tmax) + svp(tmin))
    
    ! 实际水汽压  
    ea = es * hum / 100.0d0
    
    ! 饱和水汽压曲线斜率
    delta = 4098.0d0 * svp(tmean) / (tmean + 237.3d0)**2
    
    ! 干湿表常数
    gamma = 0.665d0 * press
    
    ! Penman-Monteith方程
    etref = (delta * (rn - g) + gamma * 900.0d0 * u2 * (es - ea) / (tmean + 273.0d0)) / &
            (delta + gamma * (1.0d0 + 0.34d0 * u2))
end subroutine
```

#### 2.3.2 作物系数法
```fortran
! 实际蒸散发分配
etact = etref * kc * ks * kr
! kc: 作物系数
! ks: 水分胁迫系数  
! kr: 根系分布系数
```

### 2.4 根系吸水模型

#### 2.4.1 Feddes模型 (rootextraction.f90)
```fortran
! 水分胁迫响应函数
real(8) function stress_function(h, h1, h2, h3, h4)
    if (h >= h1) then
        stress_function = 0.0d0      ! 过湿胁迫
    else if (h >= h2) then  
        stress_function = (h - h1) / (h2 - h1)  ! 线性过渡
    else if (h >= h3) then
        stress_function = 1.0d0      ! 最适条件
    else if (h >= h4) then
        stress_function = (h - h4) / (h3 - h4)  ! 干旱胁迫
    else
        stress_function = 0.0d0      ! 萎蔫点以下
    endif
end function
```

#### 2.4.2 根系吸水分布
```fortran
! 根系吸水速率计算
do node = 1, numnod
    if (z(node) <= rootdepth) then
        ! 根长密度分布
        root_density = root_distribution(z(node), rootdepth)
        
        ! 水分胁迫系数
        stress = stress_function(h(node), h1, h2, h3, h4)
        
        ! 根系吸水速率
        rwu(node) = tpot * root_density * stress
    else
        rwu(node) = 0.0d0
    endif
end do
```

## 3. 数值方法分析

### 3.1 三对角矩阵求解器 (tridag.f90)

#### 3.1.1 Thomas算法实现
```fortran
subroutine tridag(a, b, c, r, u, n)
    ! 前向消元
    do j = 2, n
        m = a(j) / b(j-1)
        b(j) = b(j) - m * c(j-1)
        r(j) = r(j) - m * r(j-1)
    end do
    
    ! 后向替换
    u(n) = r(n) / b(n)
    do j = n-1, 1, -1
        u(j) = (r(j) - c(j) * u(j+1)) / b(j)
    end do
end subroutine
```

#### 3.1.2 算法特点
- **时间复杂度**: O(n)
- **空间复杂度**: O(n)  
- **数值稳定性**: 对角占优矩阵保证稳定性
- **并行化潜力**: 有限，但可优化内存访问

### 3.2 自适应时间步长控制

#### 3.2.1 时间步长调整策略
```fortran
! 基于收敛性的时间步长控制
if (iter > maxiter_reduce) then
    dt = max(dt * 0.5d0, dtmin)  ! 减小时间步长
    fldecdt = .true.
else if (iter < miniter_increase) then
    dt = min(dt * 1.2d0, dtmax)  ! 增大时间步长
endif
```

#### 3.2.2 稳定性判据
- 质量守恒误差 < 1e-6
- 最大压力水头变化 < 100 cm
- 迭代次数 < 20次

### 3.3 边界条件处理算法

#### 3.3.1 上边界条件 (boundtop.f90)
```fortran
! 大气边界条件
if (potential_flux > infiltration_capacity) then
    ! 产生径流
    runoff = potential_flux - infiltration_capacity
    qtop = infiltration_capacity
else if (potential_flux < 0 .and. abs(potential_flux) > evaporation_capacity) then
    ! 限制蒸发
    qtop = -evaporation_capacity
else
    ! 通量边界
    qtop = potential_flux
endif
```

#### 3.3.2 下边界条件 (boundbottom.f90)  
```fortran
! 地下水位边界
if (swbotb == 1) then
    ! 定水头边界
    h(numnod) = groundwater_level
else if (swbotb == 2) then
    ! 自由排水边界  
    qbot = k(numnod)
else if (swbotb == 3) then
    ! 定通量边界
    qbot = prescribed_flux
endif
```

## 4. 性能优化分析

### 4.1 计算瓶颈识别

#### 4.1.1 CPU密集型操作
1. **非线性方程求解**: 占总计算时间的60-70%
2. **水力特性函数计算**: 占总计算时间的15-20%
3. **矩阵求解**: 占总计算时间的10-15%
4. **I/O操作**: 占总计算时间的5-10%

#### 4.1.2 内存访问模式
- **顺序访问**: 大部分数组操作为顺序访问，缓存友好
- **随机访问**: 土层属性查找存在随机访问
- **内存带宽**: 中等强度，主要受限于计算复杂度

### 4.2 并行化潜力

#### 4.2.1 数据并行
- **土壤层并行**: 不同土壤层的某些计算可并行
- **时间并行**: 有限，由于时间依赖性
- **参数并行**: 多参数敏感性分析可并行

#### 4.2.2 任务并行
- **模块并行**: 不同物理过程模块可部分并行
- **I/O并行**: 输入输出操作可与计算重叠

## 5. Python封装技术要点

### 5.1 内存管理策略

#### 5.1.1 数组传递
```python
# f2py接口设计建议
def solve_richards_equation(
    theta: np.ndarray,     # intent(inout)
    h: np.ndarray,         # intent(inout)  
    dt: float,             # intent(in)
    numnod: int,           # intent(in)
    converged: bool        # intent(out)
) -> tuple[np.ndarray, np.ndarray, bool]:
```

#### 5.1.2 状态管理
```python
class SwapState:
    """SWAP模型状态管理类"""
    def __init__(self, numnod: int):
        self.theta = np.zeros(numnod)
        self.h = np.zeros(numnod)
        self.k = np.zeros(numnod)
        self.q = np.zeros(numnod + 1)
        self.dt = 0.01  # 初始时间步长
        
    def update_hydraulic_properties(self):
        """更新水力特性"""
        pass
        
    def check_mass_balance(self) -> float:
        """检查质量平衡"""
        pass
```

### 5.2 错误处理机制

#### 5.2.1 异常映射
```python
class SwapError(Exception):
    """SWAP模型基础异常"""
    pass

class ConvergenceError(SwapError):
    """收敛性错误"""
    pass
    
class BoundaryError(SwapError):
    """边界条件错误"""
    pass
```

### 5.3 性能优化建议

#### 5.3.1 编译优化
- 使用-O3优化级别
- 启用向量化指令
- 使用快速数学库(MKL/OpenBLAS)

#### 5.3.2 接口优化
- 减少Python-Fortran调用频率
- 批量处理数组操作
- 使用内存视图避免数据复制

这个技术分析为PySWAP-demo项目的具体实施提供了算法层面的详细指导。
